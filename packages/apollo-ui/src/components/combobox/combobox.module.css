@layer legacy {
  .comboboxMenuPositioner {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    outline: 0;
    z-index: 701;
  }


  .comboboxMenuRoot {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    border-radius: 8px;
    background: var(--apl-colors-surface-static-ui-default, #FFF);

    width: var(--anchor-width);
    outline: none;

    &>* {
      flex-shrink: 0;
    }

    & :global(.ApolloMenuItem-root) {

      &:first-of-type,
      &:last-of-type {
        border-radius: 8px;
      }
    }
  }
}

@layer apollo {
  .comboboxMenuPositioner {
    outline: 0;
    box-shadow: 4px 4px 15px 3px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 4px 4px 15px 3px rgba(0, 0, 0, 0.05);
    z-index: 701;
  }


  .comboboxMenuRoot {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    border-radius: var(--apl-alias-radius-radius4, 8px);
    background: var(--apl-alias-color-background-and-surface-background, #FFF);

    width: var(--anchor-width);
    outline: none;

    &>* {
      flex-shrink: 0;
    }

    & :global(.ApolloMenuItem-root) {

      &:first-of-type,
      &:last-of-type {
        border-radius: var(--apl-alias-radius-radius4, 8px);
      }
    }
  }
}

.comboboxRoot {
  outline: none;
  width: fit-content;
}

.comboboxRootFullWidth {
  width: 100%;
}

.comboboxMenuItem {
  width: 100%;
  outline: none;
}

.comboboxChips {
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.125rem;
  border: 1px solid var(--color-gray-200);
  border-radius: 0.375rem;
  padding: 0.25rem 0.375rem;
  width: 15rem;

  &:focus-within {
    outline: 2px solid var(--color-blue);
    outline-offset: -1px;
  }

  /* @media (min-width: 500px) {
    width: 22rem;
  } */
}

.comboboxInput {
  flex: 1;
  box-sizing: border-box;
  height: 100%;
  

  & input {
    flex: 1;
    min-width: 10px;
    border: none;
    outline: none;
    background: transparent;
    padding: 4px;
  }

  &:focus-within {
    max-height: unset;
  }
}

.comboboxInputWithChips {
  margin-top: 4px;
}

.comboboxInputEndDecorator {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.comboboxClear,
.comboboxTrigger {
  color: var(--apl-alias-color-background-and-surface-on-surface, #474647);

  &[data-disabled] {
    color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
  }
}

/* Hide Clear button when Base UI marks it hidden */
.comboboxClear[hidden],
.comboboxClear[data-hidden] {
  display: none !important;
}

.Popup {
  box-sizing: border-box;
  border-radius: 0.5rem;
  padding-block: 0.5rem;
  background-color: canvas;
  color: var(--color-gray-900);
  width: var(--anchor-width);
  max-width: var(--available-width);
  max-height: min(var(--available-height), 24rem);
  overflow-y: auto;
  scroll-padding-block: 0.5rem;
  overscroll-behavior: contain;
  transition:
    opacity 0.1s,
    transform 0.1s;
  transform-origin: var(--transform-origin);

  &[data-starting-style],
  &[data-ending-style] {
    opacity: 0;
    transform: scale(0.95);
  }

  @media (prefers-color-scheme: light) {
    outline: 1px solid var(--color-gray-200);
    box-shadow:
      0 10px 15px -3px var(--color-gray-200),
      0 4px 6px -4px var(--color-gray-200);
  }

  @media (prefers-color-scheme: dark) {
    outline: 1px solid var(--color-gray-300);
    outline-offset: -1px;
  }
}