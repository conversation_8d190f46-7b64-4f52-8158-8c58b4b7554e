import type { HTMLProps, ReactNode } from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"

import type { InputProps } from "../input"

/**
 * Option type for Combobox
 */
export type ComboboxOption = {
  label: string
  id: string
  disabled?: boolean

  renderLabel?: () => ReactNode
}

export type Group<T> = {
  label: string
  id: string
  items: T[]
}

/**
 * Helper type to determine the value type based on the multiple prop
 */
export type ComboboxValueType<
  ItemValue,
  Multiple extends boolean | undefined,
> = Multiple extends true ? ItemValue[] : ItemValue


export type ComboboxProps<
  ItemValue extends ComboboxOption,
  Multiple extends boolean | undefined = false,
> = {
  ref?: React.RefObject<HTMLDivElement>
  /**
   * Whether multiple items can be selected.
   * @default false
   */
  multiple?: Multiple
  /**
   * Array of options to display in the combobox
   */
  options?: ItemValue[]

  onValueChange?: (value: ComboboxValueType<ItemValue, Multiple> | null) => void
  size?: "medium" | "small"
  /**
   * Slot props for customizing internal components
   */
  slotProps?: ComboboxSlotProps<ItemValue>
  /**
   * When items' ids are not sufficient, converts the id to a string label for display.
   */
  itemToStringLabel?: (itemId: string) => string
} & Pick<
  InputProps,
  | "placeholder"
  | "label"
  | "fullWidth"
  | "disabled"
  | "helperText"
  | "error"
  | "required"
  | "labelDecorator"
  | "helperTextDecorator"
> &
  Omit<
    BaseCombobox.Root.Props<ItemValue, ItemValue, Multiple>,
    "onValueChange" | "children" | "items"
  >

/**
 * Slot props for customizing Combobox sub-components
 */
export type ComboboxSlotProps<ItemValue extends ComboboxOption> = {
  container?: {
    className?: string
  }
  input?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLInputElement>,
      state: BaseCombobox.Input.State
    ) => ReactNode
  }
  positioner?: {
    className?: string
  }
  popup?: {
    className?: string
  }
  option?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLDivElement> & { optionData : ComboboxOption },
      state: {
        selected: boolean
        highlighted: boolean
        disabled: boolean
      } & BaseCombobox.Item.State
    ) => ReactNode
  }
  chips?: {
    className?: string
  }
  chip?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLDivElement> & { optionData : ComboboxOption },
      state: BaseCombobox.Chip.State,
    ) => ReactNode
  }
}
